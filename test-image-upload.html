<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Image Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 16px;
            margin: 20px 0;
        }
        .image-item {
            position: relative;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .image-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }
        .remove-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #ff4444;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s;
        }
        .image-item:hover .remove-btn {
            opacity: 1;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            margin: 20px 0;
        }
        .upload-area:hover {
            background-color: #f9f9f9;
        }
        #file-input {
            display: none;
        }
        .info {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Test Image Upload Functionality</h1>
    
    <div class="info">
        <h3>Test Instructions:</h3>
        <ol>
            <li>Click the upload area below to select images</li>
            <li>Verify that images appear immediately after selection</li>
            <li>Hover over images to see the remove button</li>
            <li>Click the X button to remove images</li>
            <li>Verify that images disappear immediately when removed</li>
        </ol>
    </div>

    <div class="upload-area" onclick="document.getElementById('file-input').click()">
        <div>📁 Click to select images</div>
        <div style="font-size: 12px; color: #666; margin-top: 8px;">
            Supports multiple images: JPG, PNG
        </div>
    </div>

    <input type="file" id="file-input" accept="image/*" multiple>

    <div id="image-grid" class="image-grid"></div>

    <div id="status"></div>

    <script>
        let images = [];
        
        function updateStatus(message) {
            document.getElementById('status').innerHTML = `<div style="color: #666; font-size: 14px;">${message}</div>`;
        }

        function renderImages() {
            const grid = document.getElementById('image-grid');
            grid.innerHTML = '';
            
            images.forEach((image, index) => {
                const div = document.createElement('div');
                div.className = 'image-item';
                div.innerHTML = `
                    <img src="${image.dataUrl}" alt="Image ${index + 1}">
                    <button class="remove-btn" onclick="removeImage(${index})">×</button>
                `;
                grid.appendChild(div);
            });
            
            updateStatus(`Currently showing ${images.length} images`);
        }

        function addImage(file, dataUrl) {
            console.log('Adding image:', file.name);
            images.push({ file, dataUrl, name: file.name });
            renderImages();
        }

        function removeImage(index) {
            console.log('Removing image at index:', index);
            images.splice(index, 1);
            renderImages();
        }

        document.getElementById('file-input').addEventListener('change', function(e) {
            const files = Array.from(e.target.files || []);
            console.log('Selected files:', files.map(f => f.name));
            
            files.forEach((file, fileIndex) => {
                console.log(`Processing file ${fileIndex}:`, file.name);
                const reader = new FileReader();
                reader.onload = function(event) {
                    if (event.target?.result) {
                        console.log(`File ${fileIndex} loaded, adding to display`);
                        addImage(file, event.target.result);
                    }
                };
                reader.onerror = function() {
                    console.error(`Error reading file ${fileIndex}:`, file.name);
                };
                reader.readAsDataURL(file);
            });
            
            // Reset input
            e.target.value = '';
        });

        updateStatus('Ready to upload images');
    </script>
</body>
</html>
